import { defineConfig } from 'vite';
import * as path from 'path';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(() => ({
  define: {
    'process.env.VITE_API_HOST': JSON.stringify(process.env.VITE_API_HOST),
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __APP_TIMESTAMP__: JSON.stringify(process.env.npm_package_config_timestamp),
    APP_INFO: JSON.stringify({
      name: process.env.npm_package_name,
      version: process.env.npm_package_version,
      releaseDate: process.env.npm_package_config_releaseDate,
    }),
  },
  base: '/',
  plugins: [react()],
  resolve: {
    alias: [{ find: '@', replacement: path.resolve(__dirname, 'src') }],
  },
  optimizeDeps: {
    exclude: ['dayjs/locale/gom-latn'],
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['src/setupTest.ts'],
    threads: false,
  },
}));

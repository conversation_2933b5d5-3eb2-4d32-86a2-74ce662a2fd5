import Breadcrumb from '@/components/breadcrumb/Breadcrumb';
import ListenerViewer from '@/components/listeners/ListenerViewer';
import { ListenerCreateEdit } from '@/components/listeners/ListenerCreateEdit';
import { ViewerProvider } from '@/contexts/viewer-context/ViewerContext';
import Main from '@/layouts/main/Main';
import { ListenerCertificateUpload } from '@/components/listeners/ListenerCertificateUpload';
import { useState } from 'react';
import { Logger } from '@/utils/Logger';

function Listeners() {
  type StateType = {
    showCreatePopup: boolean;
    showEditPopup: boolean;
    showUploadPopup: boolean;
    listenerConfig: ListenerConfig | null;
  };

  const [state, setState] = useState<StateType>({
    showCreatePopup: false,
    showEditPopup: false,
    showUploadPopup: false,
    listenerConfig: null,
  });

  return (
    <>
      {(state.showCreatePopup || state.showEditPopup) && (
        <ListenerCreateEdit
          listener={state.listenerConfig}
          createMode={state.showCreatePopup}
          callback={(listener) => {
            Logger.debug(listener);
            if (!state.showCreatePopup && listener && state.listenerConfig) {
              const hasChanges =
                JSON.stringify(listener) !==
                JSON.stringify(state.listenerConfig);
              if (hasChanges) {
                setState((prevState) => ({
                  ...prevState,
                  listenerConfig: listener,
                  showEditPopup: true,
                }));
                return;
              }
            }

            if (listener == null) {
              setState((prevState) => ({
                ...prevState,
                showCreatePopup: false,
                showEditPopup: false,
                showUploadPopup: false,
                listenerConfig: null,
              }));
              return;
            }

            if (
              listener.type === 'EXTERNAL' &&
              listener?.connectionInfo?.componentInfo?.secure
            ) {
              setState((prevState) => ({
                ...prevState,
                showCreatePopup: false,
                showEditPopup: false,
                showUploadPopup: true,
                listenerConfig: listener,
              }));
            }
          }}
          close={() => {
            setState((prevState) => ({
              ...prevState,
              showCreatePopup: false,
              showEditPopup: false,
            }));
          }}
        />
      )}

      {state.showUploadPopup && state.listenerConfig && (
        <ListenerCertificateUpload
          listener={state.listenerConfig}
          callback={(listener) => {
            setState((prevState) => ({
              ...prevState,
              showCreatePopup: false,
              showEditPopup: true,
              showUploadPopup: false,
            }));
          }}
          close={() => {
            setState((prevState) => ({
              ...prevState,
              showUploadPopup: false,
            }));
          }}
        />
      )}

      <ViewerProvider>
        <Main
          breadcrumb={
            <Breadcrumb
              pages={[
                {
                  name: 'Listeners',
                  href: `/app/listeners`,
                  current: true,
                },
              ]}
            />
          }
        >
          <>
            {
              <ListenerViewer
                title="Listeners"
                addLabel="Add Listeners"
                modalOpener={(createMode, listenerConfig) => {
                  setState((prevState) => ({
                    ...prevState,
                    showCreatePopup: createMode,
                    showEditPopup: !createMode,
                    showUploadPopup: false,
                    listenerConfig: listenerConfig ?? null,
                  }));
                }}
              />
            }
          </>
        </Main>
      </ViewerProvider>
    </>
  );
}

export default Listeners;

import { useEffect, useState } from 'react';

import Main from '@/layouts/main/Main';
import DatePicker from 'react-datepicker';
import { useParams } from 'react-router-dom';
import { useBoolean } from 'usehooks-ts';

import filtersIcon from '@/assets/icons/filtersIcon.svg';
import classNames from 'classnames';
import 'react-datepicker/dist/react-datepicker.css';
import { Link } from 'react-router-dom';

import { useLoader } from '@/components/loaderIndicator/LoaderContext';
import LogItemDetailsModal from '@/components/modals/log-item-details-modal/LogItemDetailsModal';
import { useFlagcard } from '@/contexts/flagcard-context';
import { storyService } from '@/services/dojo-api-gateway/story-service';
import { faSync } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQuery } from '@tanstack/react-query';
import DataTable, { TableColumn } from 'react-data-table-component';
import mockLogs from './MockLogsData';
import Dropdown from '@/components/dropdown/Dropdown';

interface LogItem {
  '@timestamp': string;
  marker: string;
  id: string;
  currentState: string;
  previousState: string;
  uniqueIdentifier: string;
}

const setHours = function (
  date: Date,
  hours: number,
  min: number,
  sec: number
) {
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    hours,
    min,
    sec
  );
};

let currentDate = new Date();
let initStart = new Date(
  currentDate.getFullYear(),
  currentDate.getMonth() - 3,
  currentDate.getDate(),
  0,
  0,
  0
);
let initEnd = setHours(currentDate, 23, 59, 59);

function Logs() {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();
  let { storyName } = useParams();

  const detailModalVisibility = useBoolean(false);
  const [isLoading, setIsLoading] = useState(false);

  // Filter config
  const [openFilter, setOpenFilter] = useState(false);
  const [startDate, setStartDate] = useState(initStart);
  const [endDate, setEndDate] = useState(initEnd);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRow, setSelectedRow] = useState<any>();
  const [filter, setFilter] = useState<any>({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    results: pageSize,
    page: 1,
    filters: {
      uniqueIdentifier: undefined,
      previousState: undefined,
      currentState: undefined,
    },
    markers: ['STATE'],
  });

  type LogEntry = {
    currentState: string;
    previousState: string;
  };

  const [prevStates, setPrevStates] = useState<Array<string>>([]);
  const [curStates, setCurStates] = useState<Array<string>>([]);

  const extractStates = (logs: LogEntry[]): void => {
    const previousStates = new Set<string>();
    const currentStates = new Set<string>();
    logs.forEach((log: LogEntry) => {
      previousStates.add(log.previousState);
      currentStates.add(log.currentState);
    });
    setPrevStates(Array.from(previousStates).sort());
    setCurStates(Array.from(currentStates).sort());
  };

  const { data: logs, refetch: refetchLogs } = useQuery(
    ['storyLogs', { storyName, filter }],
    async () => {
      showLoader();
      const response = await storyService.story.logsByFilter(
        storyName as string,
        filter
      );
      hideLoader();

      // extractStates(mockLogs.Data.content.results as Array<LogEntry>);
      // console.log(prevStates);
      // console.log(curStates);

      // const filteredData = mockLogs.Data.content.results.filter((log) => {
      //   const logTimestamp = new Date(log['@timestamp']).getTime();
      //   const startDate = new Date(filter.startDate).getTime();
      //   const endDate = new Date(filter.endDate).getTime();

      //   const isWithinDateRange =
      //     logTimestamp >= startDate && logTimestamp <= endDate;

      //   const isMatchingUniqueIdentifier = filter.filters.uniqueIdentifier
      //     ? log.uniqueIdentifier.includes(filter.filters.uniqueIdentifier)
      //     : true;

      //   const isMatchingPreviousState = filter.filters.previousState
      //     ? log.previousState === filter.filters.previousState
      //     : true;

      //   const isMatchingCurrentState = filter.filters.currentState
      //     ? log.currentState === filter.filters.currentState
      //     : true;

      //   const isMatchingMarker = filter.markers.includes(log.marker); // Filter by 'STATE' marker

      //   return (
      //     isWithinDateRange &&
      //     isMatchingUniqueIdentifier &&
      //     isMatchingPreviousState &&
      //     isMatchingCurrentState &&
      //     isMatchingMarker
      //   );
      // });

      // const updatedLogs = {
      //   Data: {
      //     content: {
      //       numResults: filteredData.length,
      //       numPages: Math.ceil(filteredData.length / filter.results),
      //       results: filteredData,
      //     },
      //   },
      // };

      // return updatedLogs.Data.content;
      //return mockLogs.Data.content;
      return response.data?.Data.content;
    },
    {
      onError: (error: any) => {
        flagcard.appear({
          type: 'error',
          message: error.message,
        });
        hideLoader();
      },
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    }
  );

  const onChangeFilterUID = (event: React.ChangeEvent<HTMLInputElement>) => {
    const filterUID = event.target.value;
    setFilter((prevState: any) => ({
      ...prevState,
      filters: {
        ...prevState.filters,
        uniqueIdentifier: filterUID.length > 0 ? filterUID : undefined,
      },
    }));
  };
  const onChangePreviousState = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newPreviousState = event.target.value;
    setFilter((prevState: any) => ({
      ...prevState,
      filters: {
        ...prevState.filters,
        previousState:
          newPreviousState.length > 0 ? newPreviousState : undefined,
      },
    }));
  };
  const onChangeCurrentState = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newCurrentState = event.target.value;
    setFilter((prevState: any) => ({
      ...prevState,
      filters: {
        ...prevState.filters,
        currentState: newCurrentState.length > 0 ? newCurrentState : undefined,
      },
    }));
  };
  const onChangeStartDate = (date: Date) => {
    const newStartDate = setHours(date, 0, 0, 0);
    setStartDate(newStartDate);
    setFilter((prevState: any) => ({
      ...prevState,
      startDate: newStartDate.toISOString(),
    }));
  };
  const onChangeEndDate = (date: Date) => {
    const newEndDate = setHours(date, 23, 59, 59);
    setEndDate(newEndDate);
    setFilter((prevState: any) => ({
      ...prevState,
      endDate: newEndDate.toISOString(),
    }));
  };
  const onChangeMarker = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newMarkers = [...filter.markers];
    if (event.target.checked) newMarkers.push(event.target.id);
    else newMarkers.splice(newMarkers.indexOf(event.target.id, 0), 1);
    setFilter((prevState: any) => ({
      ...prevState,
      markers: newMarkers,
    }));
  };
  const onPageChange = (page: any) => {
    setPage(page);
    setFilter((prevState: any) => ({
      ...prevState,
      page: page,
    }));
  };
  const onPageSizeChange = (pageSize: any, page: any) => {
    setPage(page);
    setPageSize(pageSize);
    setFilter((prevState: any) => ({
      ...prevState,
      page: page,
      results: pageSize,
    }));
  };

  const resetHandler = () => {
    setIsLoading(true);
    let tempDate = new Date();
    tempDate.setMonth(tempDate.getMonth() - 3);
    const initStart = setHours(tempDate, 0, 0, 0);
    tempDate = new Date();
    // tempDate.setMonth(tempDate.getMonth() + 1); // Next month
    const initEnd = setHours(tempDate, 23, 59, 59);

    setPageSize(10);
    setPage(1);
    setStartDate(initStart);
    setEndDate(initEnd);

    setFilter((prevState: any) => ({
      startDate: initStart.toISOString(),
      endDate: initEnd.toISOString(),
      results: pageSize,
      page: page,
      filters: {
        uniqueIdentifier: undefined,
        previousState: undefined,
        currentState: undefined,
      },
      markers: ['STATE'],
    }));

    setTimeout(() => {
      setIsLoading(false);
    }, 750);
  };

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 750);
  }, [filter]);

  const setAndOpenModalHandler = (data: any) => {
    setSelectedRow(data);
    detailModalVisibility.setTrue();
  };

  const columns: TableColumn<LogItem>[] = [
    {
      id: 'marker',
      name: 'Log Type',
      selector: (row) => row.marker,
      sortable: true,
      wrap: true,
      reorder: true,
      cell: (row) => (
        <div className="font-light text-[12px] text-gray-500">{row.marker}</div>
      ),
    },
    {
      id: 'uniqueIdentifier',
      name: 'Player Name',
      selector: (row) => row.uniqueIdentifier,
      sortable: true,
      wrap: true,
      reorder: true,
      cell: (row) => (
        <div className="font-light text-[12px] text-gray-500">
          {row.uniqueIdentifier}
        </div>
      ),
    },
    {
      id: 'previousState',
      name: 'Previous State	',
      selector: (row) => row.previousState,
      sortable: true,
      wrap: true,
      reorder: true,
      cell: (row) => (
        <div className="font-light text-[12px] text-gray-500">
          {row.previousState ?? 'None'}
        </div>
      ),
    },
    {
      id: 'currentState',
      name: 'Current State',
      selector: (row) => row.currentState,
      sortable: true,
      wrap: true,
      reorder: true,
      cell: (row) => (
        <div className="font-light text-[12px] text-gray-500">
          {row.currentState ?? 'None'}
        </div>
      ),
    },
    {
      id: '@timestamp',
      name: 'Date',
      selector: (row) => row['@timestamp'],
      sortable: true,
      wrap: true,
      reorder: true,
      cell: (row) => (
        <div className="font-light text-[12px] text-gray-500">
          {new Intl.DateTimeFormat(navigator.language, {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
          }).format(new Date(row['@timestamp']))}
        </div>
      ),
    },
    {
      id: 'details',
      name: 'Details',
      selector: (row) => row.id,
      sortable: false,
      wrap: true,
      reorder: true,
      cell: (row) => {
        return (
          <span className="px-6 py-4 text-[12px] font-light italic underline">
            <a href="#" onClick={() => setAndOpenModalHandler(row)}>
              {'Show Details'}
            </a>
          </span>
        );
      },
    },
  ];

  const paginationComponentOptions = {
    rowsPerPageText: 'Row per page',
    rangeSeparatorText: 'of',
    selectAllRowsItem: true,
    selectAllRowsItemText: 'All',
  };

  const customStyles = {
    table: {
      style: {
        border: 'none',
      },
    },
    headRow: {
      style: {
        height: '30px',
        'border-bottom': '1px solid #ededed',
      },
    },
    headCells: {
      style: {
        height: '30px',
        'border-left': '1px solid #ededed',
        'border-right': '1px solid #ededed',
      },
    },
    rows: {
      style: {
        border: 'none !important',
      },
    },
    cells: {
      style: {
        border: 'none',
      },
    },
  };

  return (
    <>
      {detailModalVisibility.value && (
        <LogItemDetailsModal
          key={'log-item-details'}
          item={selectedRow}
          storyName={storyName ?? ''}
          visibility={detailModalVisibility}
        />
      )}
      <Main>
        <div className="w-full mt-5 relative">
          <div className="absolute -top-9 left-0 text-black font-bold text-2xl">
            Logs
          </div>
          <div className="flex items-center justify-between">
            <div className="flex flex-row items-center bg-black overflow-hidden rounded-md select-none">
              <Link
                className="px-4 justify-between items-center rounded-lg z-40 h-11 bg-black text-white shadow flex font-semibold text-sm whitespace-nowrap"
                to={`/app/story/${storyName}`}
              >
                Back to
              </Link>
              <div className="h-6 w-[0.15rem] rounded-full bg-gray-100"></div>
              <button
                type="button"
                onClick={() => {
                  refetchLogs().then(() => {});
                }}
                className="text-sm font-semibold h-11 flex justify-center items-center py-0 px-4 rounded-md shadow-sm text-white bg-black focus:outline-none"
              >
                <FontAwesomeIcon icon={faSync} size="lg" />
              </button>
            </div>

            <div className="flex flex-col items-center justify-center gap-4">
              <div
                onClick={() => {
                  setOpenFilter(!openFilter);
                }}
                className={
                  openFilter
                    ? 'flex w-full items-center rounded-md justify-center gap-2 select-none cursor-pointer font-semibold py-2 px-2 bg-[#B3B3B3] text-white border-2 border-black'
                    : 'flex w-full items-center rounded-md justify-center gap-2 select-none cursor-pointer font-semibold py-2 px-2 text-[#B3B3B3] bg-[#EDEDED] border-2 border-transparent'
                }
              >
                <img src={filtersIcon} /> <span className="pr-10">Filters</span>
              </div>
              <div
                className={classNames(
                  'block w-72 absolute border-2 border-black rounded-lg top-11 right-0 z-40 px-3 bg-[#EDEDED] shadow-md',
                  !openFilter && 'hidden'
                )}
              >
                <div className="flex flex-col justify-start my-3 gap-5">
                  <div>
                    <label className="block text-sm font-medium">
                      Player Name
                    </label>
                    <input
                      className="w-64 appearance-none block px-3 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:ring-1 focus:border-black sm:text-sm text-black"
                      onChange={onChangeFilterUID}
                      placeholder="Player Name"
                      value={filter.filters.uniqueIdentifier ?? ''}
                      autoFocus
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">
                      Previous State
                    </label>
                    <select
                      className="block w-64 pl-2 pr-10 py-2 text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md"
                      value={filter.filters.previousState ?? 'ALL_STATES'} // Ensure a valid option
                      onChange={(e) => {
                        const val = e.currentTarget.value;
                        setFilter((prevState: any) => ({
                          ...prevState,
                          filters: {
                            ...prevState.filters,
                            previousState:
                              val !== 'ALL_STATES' ? val : undefined, // Reset when "All States" is selected
                          },
                        }));
                      }}
                    >
                      <option value="ALL_STATES">All States</option>
                      {logs?.previous_states &&
                        logs?.previous_states.map((state: any) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))}
                      {/* {prevStates &&
                        prevStates.map((state) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))} */}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium">
                      Current State
                    </label>
                    <select
                      className="block w-64 pl-2 pr-10 py-2 text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md"
                      value={filter.filters.currentState ?? 'ALL_STATES'} // Use a valid option value
                      onChange={(e) => {
                        const val = e.currentTarget.value;
                        setFilter((prevState: any) => ({
                          ...prevState,
                          filters: {
                            ...prevState.filters,
                            currentState:
                              val !== 'ALL_STATES' ? val : undefined,
                          },
                        }));
                      }}
                    >
                      <option value="ALL_STATES">All States</option>
                      {logs?.current_states &&
                        logs?.current_states.map((state: any) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))}
                      {/* {curStates &&
                        curStates.map((state) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))} */}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium">
                      Start Date
                    </label>
                    <DatePicker
                      className="block w-64 appearance-none px-3 py-2 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:ring-1 focus:border-black sm:text-sm text-black"
                      selected={startDate}
                      onChange={onChangeStartDate}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">
                      End Date
                    </label>
                    <DatePicker
                      className="block w-64 my-auto appearance-none px-3 py-2 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:ring-1 focus:border-black sm:text-sm text-black"
                      selected={endDate}
                      onChange={onChangeEndDate}
                    />
                  </div>
                  <div className="my-auto">
                    <div style={{ float: 'left', marginRight: '10px' }}>
                      <input
                        id="STATE"
                        type="checkbox"
                        className="appearance-none block w-3 h-3 p-2 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black focus:border-2 sm:text-sm text-black disabled:border-gray-300"
                        checked={filter.markers.indexOf('STATE') > -1}
                        onChange={onChangeMarker}
                      />
                    </div>
                    <label>Show state logs</label>
                  </div>
                  <div className="my-auto">
                    <div style={{ float: 'left', marginRight: '10px' }}>
                      <input
                        id="ACTION"
                        type="checkbox"
                        className="appearance-none block w-3 h-3 p-2 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black focus:border-2 sm:text-sm text-black disabled:border-gray-300"
                        checked={filter.markers.indexOf('ACTION') > -1}
                        onChange={onChangeMarker}
                      />
                    </div>
                    <label>Show action logs</label>
                  </div>
                  <div className="my-auto">
                    <div style={{ float: 'left', marginRight: '10px' }}>
                      <input
                        id="CONDITION"
                        type="checkbox"
                        className="appearance-none block w-3 h-3 p-2 border-2 border-black rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black focus:border-2 sm:text-sm text-black disabled:border-gray-300"
                        checked={filter.markers.indexOf('CONDITION') > -1}
                        onChange={onChangeMarker}
                      />
                    </div>
                    <label>Show condition logs</label>
                  </div>
                  <button
                    className="font-semibold text-[#808184]"
                    onClick={() => resetHandler()}
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="py-5">
            <DataTable
              columns={columns}
              responsive
              noDataComponent={<div>No records found</div>} // Custom no data message
              data={logs?.results || []}
              pagination
              paginationServer
              paginationTotalRows={logs?.numResults || 0}
              paginationRowsPerPageOptions={[10, 20, 50, 100]}
              onChangePage={onPageChange}
              onChangeRowsPerPage={onPageSizeChange}
              paginationPerPage={pageSize}
              paginationComponentOptions={paginationComponentOptions}
              defaultSortAsc
              customStyles={customStyles}
            />
          </div>
        </div>
      </Main>
    </>
  );
}

export default Logs;

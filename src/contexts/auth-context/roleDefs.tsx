export const enum Roles {
  COLLECTION = 'collection',
  COLLECTION_READ = 'collection_read',
  COLLECTION_CREATE = 'collection_create',
  COLLECTION_UPDATE = 'collection_update',
  COLLECTION_DELETE = 'collection_delete',

  STORY = 'story',
  STORY_READ = 'story_read',
  STORY_CREATE = 'story_create',
  STORY_UPDATE = 'story_update',
  STORY_DELETE = 'story_delete',

  LIBRARY = 'library',
  LIBRARY_READ = 'library_read',
  LIBRARY_CREATE = 'library_create',
  LIBRARY_UPDATE = 'library_update',
  LIBRARY_DELETE = 'library_delete',

  LISTENER = 'listener',
  LISTENER_READ = 'listener_read',
  LISTENER_CREATE = 'listener_create',
  LISTENER_UPDATE = 'listener_update',
  LISTENER_DELETE = 'listener_delete',

  DASHBOARD = 'dashboard',
  DASHBOARD_VIEWER = 'dashboard_viewer',
  DASHBOARD_EDITOR = 'dashboard_editor',
  DASHBOARD_ALL_ACCESS = 'dashboard_all_access',

  MANAGEMENT = 'management',
  MANAGEMENT_READ = 'management_read',
  MANAGEMENT_CREATE = 'management_create',
  MANAGEMENT_UPDATE = 'management_update',
  MANAGEMENT_DELETE = 'management_delete',

  REPORTS = 'reporting',
  REPORTS_READ = 'reporting_read',
  REPORTS_CREATE = 'reporting_create',
  REPORTS_UPDATE = 'reporting_update',
  REPORTS_DELETE = 'reporting_delete',

  INSIGHTS = 'insights',
}

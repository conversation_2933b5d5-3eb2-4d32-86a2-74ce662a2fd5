import editIcon from '@/assets/icons/edit.svg';
import trashIcon from '@/assets/icons/new-trash.svg';
import threeDotsIcon from '@/assets/icons/threedots.svg';
import Switch from '@/components/switch/Switch';
import { userRoles } from '@/contexts/auth-context/useAuth';
import { useFlagcard } from '@/contexts/flagcard-context';
import useBoolean from '@/hooks/useBoolean';
import {
  startListener,
  stopListener,
} from '@/services/dojo-api-gateway/listener-service/listener';
import { Logger } from '@/utils/Logger';
import classNames from 'classnames';
import React, { FC, PropsWithChildren, useEffect, useState } from 'react';
import { ColorRing } from 'react-loader-spinner';
import Dropdown from '../dropdown/Dropdown';
import ListenerDetailModal from './ListenerDetailModal';
import { ListenerItemProps } from './ListenerItem.specs';
import { Utils } from './ListenerUtils';

const ListenerItem: FC<PropsWithChildren<ListenerItemProps>> = ({
  item,
  cbOnEdit,
  cbOnDelete,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isActive, setIsActive] = useState(item?.status === 'UP');
  const flagcard = useFlagcard();
  const roles = userRoles();

  // Update isActive when item status changes
  useEffect(() => {
    setIsActive(item?.status === 'UP');
  }, [item?.status]);

  const switchHandler = async (val: boolean) => {
    setIsLoading(true);
    try {
      if (val) {
        const response = await startListener(item.id);
        setIsActive(true);
        flagcard.appear({
          type: 'success',
          message: response.data.Message
        });
      } else {
        const response = await stopListener(item.id);
        setIsActive(false);
        flagcard.appear({
          type: 'success',
          message: response.data.Message
        });
      }
    } catch (err: any) {
      flagcard.appear({
        type: 'error',
        message: err.response.data.Error.content[0]
      });
      // Revert switch state on error
      setIsActive(!val);
    } finally {
      setIsLoading(false);
    }
  };

  let actionIcons: Array<any> = new Array();
  const allowedActions = () => {
    if (roles.updateListener) {
      actionIcons.push({
        title: 'Edit Listener',
        icon: editIcon,
        onClick: () => {
          cbOnEdit(item);
        },
      });
    }

    if (roles.deleteListener) {
      actionIcons.push({
        title: 'Delete Listener',
        icon: trashIcon,
        onClick: () => {
          cbOnDelete(item);
        },
      });
    }

    if (actionIcons.length > 0) {
      return (
        <Dropdown
          title={<img src={threeDotsIcon} />}
          showChevron={false}
          groups={[actionIcons]}
        />
      );
    }
    return <div>-</div>;
  };

  return (
    <>
      {/* {infoModalVisibility.isVisible && (
        <ListenerDetailModal item={item} visibility={infoModalVisibility} />
      )} */}
      <div
        className={classNames(
          ' w-full border-2 border-black rounded-2xl flex-1 relative bg-[#EDEDED]',
          { 'cursor-pointer': roles.updateListener }
        )}
        onDoubleClick={() => {
          if (roles.updateListener) {
            cbOnEdit(item);
          }
        }}
      >
        {isLoading && (
          <div
            className={classNames(
              'absolute w-full h-full bg-[#ffffff66] flex items-center justify-center place-items-center rounded-xl z-50'
            )}
          >
            <ColorRing
              visible={true}
              height="80"
              width="80"
              ariaLabel="color-ring-loading"
              wrapperStyle={{}}
              wrapperClass="color-ring-wrapper"
              colors={['#6f33a1', '#6f33a1', '#6f33a1', '#6f33a1', '#6f33a1']}
            />
          </div>
        )}

        <div className="pt-3 flex justify-between">
          <div className="text-md h-fit w-fit font-bold bg-black text-white px-2 rounded-r-xl">
            {item.connectionInfo?.componentInfo?.component}
          </div>
          <div>{allowedActions()}</div>
        </div>

        <div className="relative p-1 rounded-3xl">
          <div className="w-full rounded-lg">
            <div className="flex justify-between">
              <div className="pl-2 pt-2">
                {/* <div className="text-md h-6 font-bold bg-black text-white px-2 rounded-lg">
                  {item.connectionInfo?.componentInfo?.component}
                </div> */}
                {/* {item.connectionInfo?.componentInfo?.component === 'KAFKA' && (
                  <img src={IconKafka} alt="" />
                )}
                {item.connectionInfo?.componentInfo?.component === 'TCP' && (
                  <img src={IconTcp} alt="" />
                )}
                {item.connectionInfo?.componentInfo?.component === 'UDP' && (
                  <img src={IconUdp} alt="" />
                )}
                {item.connectionInfo?.componentInfo?.component === 'HTTP' && (
                  <img src={IconHttp} alt="" />
                )}
                {item.connectionInfo?.componentInfo?.component === 'MQTT' && (
                  <img src={IconMqtt} alt="" />
                )} */}
              </div>
              {/* <div>{allowedActions()}</div> */}
            </div>

            <div className="p-3 text-black">
              {Logger.isEnabled() && (
                <div>
                  <div className="flex-grow font-thin text-sm">{item?.id}</div>
                  <div className="flex-grow font-thin text-sm">
                    {item?.eventId}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-[100px_minmax(0,1fr)]">
                <span className="text-md font-bold">Name</span>
                <p className="text-md truncate" title={item?.name}>
                  {item?.name}
                </p>

                <span className="text-md font-bold">Description</span>
                <div className="bg-transparent border-none">
                  <textarea
                    className="text-md w-full bg-transparent resize-none border-none p-0 h-auto cursor-pointer"
                    disabled
                    value={item?.description}
                  />
                </div>
              </div>

              <div className="grid grid-cols-[100px_minmax(0,1fr)] my-5">
                <span className="text-md font-bold">Source</span>
                <p className="text-md">{`${item?.type}`}</p>

                <span className="text-md font-bold">Protocol</span>
                <p className="text-md">
                  {`${
                    item?.connectionInfo.componentInfo.secure
                      ? 'SECURE'
                      : 'NON-SECURE'
                  }/${item?.connectionInfo.componentInfo.component}`}
                </p>

                <span className="text-md font-bold">Event Name</span>
                <p className="text-md">{item.eventName}</p>

                {item.version && (
                  <div>
                    <span className="text-md font-bold">Version</span>
                    <p className="text-sm">{item.version}</p>
                  </div>
                )}
              </div>
              <p className="w-full text-right text-gray-400 text-sm mb-4">
                Created by {item.user} at{' '}
                {Utils.formatISODate(item?.createdDate)}
              </p>

              <div className="flex justify-between items-end my-2">
                <div className="flex items-center">
                  <label className="relative inline-flex items-center cursor-pointer">
                    {roles.updateListener && (
                      <div
                        title={
                          !roles.updateListener
                            ? "You don't have permission to update listeners"
                            : 'You have permission to update listeners, click to toggle'
                        }
                      >
                        <Switch
                          label={''}
                          checked={isActive}
                          disabled={!roles.updateListener || isLoading}
                          onChange={switchHandler}
                        />
                      </div>
                    )}
                  </label>
                  <span className="text-gray-500">
                    {isActive ? 'Working' : 'Stopped'}
                  </span>
                </div>

                {/* <button
                  className="text-purple-600 hover:underline"
                  onClick={infoModalVisibility.show}
                >
                  <u>Show Details</u>
                </button> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(ListenerItem);

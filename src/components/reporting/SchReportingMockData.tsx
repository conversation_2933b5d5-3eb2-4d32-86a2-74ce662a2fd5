import { storyService } from '@/services/dojo-api-gateway/story-service';
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';

import React, { useState } from 'react';
import Cron from 'react-js-cron';
import { useLoader } from '../loaderIndicator/LoaderContext';

export interface ScheduledReport {
  id: string;
  name: string;
  description: string;
  active: boolean;
  eventId: string;
  eventName: string;
  filterInfo: FilterInfo;
  scheduleInfo: ScheduleInfo;
  deliveryInfo: DeliveryInfo;
  createdAt: string;
  updatedAt: string | null;
}

export interface FilterInfo {
  filters: {
    [field: string]: FilterCondition;
  };
  operator: 'AND' | 'OR';
  sort: {
    field: string;
    asc: boolean;
  };
}

export interface ScheduleInfo {
  cronExpression: string;
  readableCronExpression: string;
  type: 'WEEKLY' | 'DAILY' | 'MONTHLY' | string;
  config: {
    hour: number;
    minute: number;
    daysOfWeek?: string[]; // as string numbers e.g. ["1", "5"]
    dayOfMonth?: number | null;
  };
  lastRunAt: string | null;
  nextRunAt: string | null;
}

export interface DeliveryInfo {
  target: {
    targetType: 'EMAIL' | string;
    targetRecipients: string[];
  };
  status: 'UNKNOWN' | 'PENDING' | 'SENT' | string;
  lastAttemptAt: string | null;
  failureDetails: string | null;
}

export type FilterCondition =
  | {
      gte?: number | string;
      lte?: number | string;
      eq?: number | string | boolean;
      like?: string;
      gt?: number | string;
      lt?: number | string;
    }
  | {}; // allow empty filters

export const SchReportingMockData: React.FC<{
  eventId: string;
  close: () => void;
}> = ({ eventId }) => {
  const [value, setValue] = useState('0 0 * * *');
  const [selectedReport, setSelectedReport] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    targetRecipients: [],
    filters: {},
    active: false,
  });
  const { showLoader, hideLoader } = useLoader();

  const { data: schReports, refetch: refetchSchReport } = useQuery({
    queryKey: ['schReports'],
    queryFn: async () => {
      showLoader();
      const response = await storyService.story.scheduledReports(eventId);
      hideLoader();
      return response.data?.Data?.content;
    },
    onError: () => {
      // flagcard.appear({
      //   type: 'error',
      //   message: error.message,
      // });
      // hideLoader();
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled: true,
  });

  const handleChange = (event: SelectChangeEvent) => {
    const selectedName = event.target.value;
    setSelectedReport(selectedName);
    // Find the selected schedule and update the form data
    const schedule = schReports?.find(
      (s: ScheduledReport) => s.name === selectedName
    );
    if (schedule) {
      setFormData({
        ...formData,
        name: schedule.name,
        targetRecipients: schedule.deliveryInfo.target.targetRecipients as [],
        filters: schedule.filterInfo.filters,
        active: schedule.active,
      });
    }
  };

  return (
    <Box>
      <FormControl fullWidth>
        <InputLabel id="scheduled-report-label">
          Scheduled Reportings
        </InputLabel>
        <Select
          labelId="scheduled-report-label"
          id="scheduled-report-select"
          value={selectedReport}
          label="Scheduled Reportings"
          onChange={handleChange}
        >
          {schReports?.map((schedule: ScheduledReport, index: number) => (
            <MenuItem key={index} value={schedule.name}>
              {schedule.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth sx={{ mt: 2 }}>
        <TextField
          label="Name"
          fullWidth
          value={formData.name}
          onChange={(e) =>
            setFormData({
              ...formData,
              name: e.target.value,
            })
          }
        />
      </FormControl>
      <FormControl>
        <p id="scheduled-report-label">Interval</p>
        <Cron value={value} setValue={setValue} />
        <p style={{ marginTop: '1rem' }}>
          <strong>CRON Expression:</strong> {value}
        </p>
      </FormControl>
      <FormControl fullWidth sx={{ mt: 2 }}>
        <TextField
          label="Filters"
          value={JSON.stringify(formData.filters, null, 2)}
          multiline
          rows={6}
          variant="outlined"
        />
      </FormControl>
      <FormControl fullWidth sx={{ mt: 2 }}>
        TARGET EMAIL
      </FormControl>
      <FormControl fullWidth sx={{ mt: 2 }}>
        <TextField
          label="Email List"
          value={formData.targetRecipients.join('\n')}
          multiline
          rows={6}
          variant="outlined"
        />
      </FormControl>
      <FormControlLabel
        control={
          <Checkbox
            checked={formData.active}
            onChange={(e) =>
              setFormData({
                ...formData,
                active: e.target.checked,
              })
            }
          />
        }
        label="Active"
      />
      <FormControl fullWidth sx={{ mt: 2 }}>
        DELIVERY STATUS ETC.
      </FormControl>
    </Box>
  );
};

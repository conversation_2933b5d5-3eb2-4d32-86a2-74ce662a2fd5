import { useFlagcard } from '@/contexts/flagcard-context';
import { storyService } from '@/services/dojo-api-gateway/story-service';

import { Logger } from '@/utils/Logger';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import useEvent from 'react-use-event-hook';
import { useLoader } from '../loaderIndicator/LoaderContext';

import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import Cron from 'react-js-cron';
import Switch from '../switch/Switch';
import {
  createSchReporting,
  deleteSchReport,
  updateSchReport,
} from '@/services/dojo-api-gateway/story-service/story/story';

export interface ScheduledReport {
  id: string;
  name: string;
  description: string;
  status: string;
  eventId: string;
  eventName: string;
  filterInfo: FilterInfo;
  scheduleInfo: ScheduleInfo;
  deliveryInfo: DeliveryInfo;
  createdAt: string;
  updatedAt: string | null;
}

export interface FilterInfo {
  filters: {
    [field: string]: FilterCondition;
  };
  operator: 'AND' | 'OR';
  sort: {
    field: string;
    asc: boolean;
  };
}

export interface ScheduleInfo {
  cronExpression: string;
  readableCronExpression: string;
  type: 'WEEKLY' | 'DAILY' | 'MONTHLY' | string;
  config: {
    hour: number;
    minute: number;
    daysOfWeek?: string[]; // as string numbers e.g. ["1", "5"]
    dayOfMonth?: number | null;
  };
  lastRunAt: string | null;
  nextRunAt: string | null;
}

export interface DeliveryInfo {
  target: {
    targetType: 'EMAIL' | string;
    targetRecipients: string[];
  };
  status: 'UNKNOWN' | 'PENDING' | 'SENT' | string;
  lastAttemptAt: string | null;
  failureDetails: string | null;
}

export type FilterCondition =
  | {
      gte?: number | string;
      lte?: number | string;
      eq?: number | string | boolean;
      like?: string;
      gt?: number | string;
      lt?: number | string;
    }
  | {}; // allow empty filters

export const SchReportCreateEdit: React.FC<{
  createMode: Boolean;
  schReport?: ScheduledReport | null;
  eventId: string;
  callback: (currentListener?: ListenerConfig) => void;
  close: () => void;
}> = ({ createMode, schReport, eventId, callback, close }) => {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();
  Logger.debug(schReport);

  const portMin = 0;
  const portMax = 65535;

  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [currentSchReport, setCurrentSchReport] = useState<ScheduledReport>(
    schReport ?? ({} as ScheduledReport)
  );

  useEffect(() => {
    const hasChanges =
      JSON.stringify(currentSchReport) !== JSON.stringify(currentSchReport);
    setCanSave(hasChanges);
  }, [currentSchReport]);

  const [value, setValue] = useState('0 0 * * *');
  const [selectedSchReport, setSelectedSchReport] = useState('');
  const [formData, setFormData] = useState<ScheduledReport>({
    name: '',
    scheduleInfo: {} as ScheduleInfo,
    deliveryInfo: {} as DeliveryInfo,
    filterInfo: {} as FilterInfo,
    status: 'PASSIVE',
  } as unknown as ScheduledReport);

  const { data: schReports, refetch: refetchSchReport } = useQuery({
    queryKey: ['schReports'],
    queryFn: async () => {
      showLoader();
      const response = await storyService.story.scheduledReports(eventId);
      hideLoader();
      return response.data?.Data?.content;
    },
    onError: () => {
      // flagcard.appear({
      //   type: 'error',
      //   message: error.message,
      // });
      // hideLoader();
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled: true,
  });

  // const handleChange = (event: SelectChangeEvent) => {
  //   const selectedName = event.target.value;
  //   setSelectedReport(selectedName);
  //   // Find the selected schedule and update the form data
  //   const schedule = schReports?.find(
  //     (s: ScheduledReport) => s.name === selectedName
  //   );
  //   if (schedule) {
  //     //schedule.status = 'ACTIVE';
  //     setFormData({
  //       ...formData,
  //       name: schedule.name,
  //       scheduleInfo: schedule.scheduleInfo,
  //       deliveryInfo: schedule.deliveryInfo,
  //       filterInfo: schedule.filterInfo,
  //       status: schedule.status !== 'PASSIVE',
  //     });
  //   }
  // };

  const onCreateClick = useEvent(() => {
    let result: any;
    showLoader();
    Logger.debug(currentSchReport);

    let createdListener: any;
    createSchReporting(currentSchReport)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        createdListener = response.data.Data.content;
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (createdListener != null) {
          refetchSchReport().then(() => {
            if (
              createdListener.type === 'EXTERNAL' &&
              createdListener?.connectionInfo?.componentInfo?.secure
            ) {
              callback(createdListener);
            } else {
              close();
            }
          });
        }
      });
  });

  const onSaveClick = useEvent(() => {
    let result: any;
    Logger.debug(currentSchReport);
    showLoader();

    const schReportToUpdate = { ...formData };

    // deleteSchReport(currentSchReport.id as string)
    //   .then((response: any) => {
    //     result = { type: 'success', message: response.data.Message };
    //     refetchSchReport();
    //   })
    //   .catch((err: any) => {
    //     result = {
    //       type: 'error',
    //       message: err.response.data.Error.content[0],
    //     };
    //   })
    //   .finally(() => {
    //     flagcard.appear({
    //       type: result.type,
    //       message: result.message,
    //     });
    //     hideLoader();
    //   });

    let updatedSchReport: any;
    updateSchReport(schReportToUpdate)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        updatedSchReport = response.data.Data.content;
        //setCurrentSchReport(updatedSchReport);
        console.log(currentSchReport);
        if (schReport) {
          callback(updatedSchReport);
        }
        Logger.debug(updatedSchReport);
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (updatedSchReport != null) {
          refetchSchReport().then(() => {});
        }
      });
  });

  const onDeleteClick = useEvent(() => {
    let result: any;
    Logger.debug(currentSchReport);
    showLoader();
    deleteSchReport(currentSchReport.id as string)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        refetchSchReport();
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        flagcard.appear({
          type: result.type,
          message: result.message,
        });
        hideLoader();
      });
  });

  const onNextClick = useEvent(() => {
    // save required ?
    //callback(currentListener);
  });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="w-[420px] min-h-[580px] max-h-[680px]  bg-[#ededed] shadow-lg rounded-xl  flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center border-gray-300 p-4">
          <h3 className="text-lg font-medium">
            {createMode ? 'New Scheduled Reporting' : currentSchReport.name}
          </h3>
          <button
            className="text-gray-600 hover:text-gray-800"
            onClick={() => {
              close();
            }}
            aria-label="Close"
          >
            &times;
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-scroll">
          <div style={{ display: 'block' }}>
            {/* Section 1 -> ListenerName | Description | Event */}
            <div className="px-5 pb-5">
              <div className="bg-[#E3E4E9] rounded-xl p-3">
                <div className="flex flex-col gap-2 items-center w-full z-10 overflow-x-auto shadow-none">
                  <Box>
                    <FormControl fullWidth>
                      <div className="flex flex-col w-full text-black rounded-xl"></div>
                      <label className="block text-sm font-medium">
                        Scheduled Reportings
                      </label>
                      <select
                        className="block w-full pl-2 pr-10 py-2 text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md"
                        id="scheduled-report-select"
                        value={selectedSchReport}
                        onChange={(e) => {
                          const selectedName = e.target.value;
                          setSelectedSchReport(selectedName);
                          // Find the selected schedule and update the form data
                          const schedule = schReports?.find(
                            (s: ScheduledReport) => s.name === selectedName
                          );
                          setCurrentSchReport(schedule);
                          if (schedule) {
                            //schedule.status = 'ACTIVE';
                            setFormData(schedule);
                          }
                        }}
                      >
                        <option value="" disabled>
                          Select an scheduled reporting
                        </option>
                        {schReports &&
                          schReports?.map(
                            (schedule: ScheduledReport, index: number) => (
                              <option key={index} value={schedule.name}>
                                {schedule.name}
                              </option>
                            )
                          )}
                      </select>
                    </FormControl>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">Name</label>
                      <input
                        className="w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="name"
                        placeholder="scheduled_report_1"
                        autoComplete="off"
                        defaultValue={formData?.name}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            name: e.target.value,
                          })
                        }
                      />
                    </FormControl>

                    {/* Cron */}
                    <FormControl>
                      <p id="scheduled-report-label">Interval</p>
                      <Cron value={value} setValue={setValue} />
                      <p style={{ marginTop: '1rem' }}>
                        <strong>CRON Expression:</strong>{' '}
                        {formData.scheduleInfo?.cronExpression}
                      </p>
                    </FormControl>

                    {/* Filters */}
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Filters
                      </label>
                      <textarea
                        className="w-full min-h-32 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="filters"
                        autoComplete="off"
                        disabled={true}
                        value={JSON.stringify(formData.filterInfo, null, 2)}
                      />
                    </FormControl>

                    {/* Target Emails */}
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Target Emails
                      </label>
                      <textarea
                        className="w-full min-h-32 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="targetEmails"
                        autoComplete="off"
                        placeholder="<EMAIL>"
                        value={formData.deliveryInfo?.target?.targetRecipients?.join(
                          '\n'
                        )}
                      />
                    </FormControl>
                    <FormControl>
                      <label className="block text-sm font-medium">
                        Status1 {formData.status}
                      </label>
                      <Switch
                        label={''}
                        checked={formData.status !== 'PASSIVE'}
                        // defaultChecked={formData.status !== 'PASSIVE'}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            status: e ? 'ACTIVE' : 'PASSIVE',
                          })
                        }
                      />
                    </FormControl>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      DELIVERY STATUS ETC.
                    </FormControl>
                  </Box>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer with Create Button */}
        <div className="p-3 border-t border-gray-300">
          {!createMode && (
            <div className="flex flex-col items-center w-full z-10 overflow-x-auto shadow-none">
              <div className="flex flex-col w-full text-left rounded-xl gap-0 px-4 pb-2"></div>
            </div>
          )}
          <div className="flex justify-end">
            {!createMode && (
              <button
                // disabled={currentListener?.component === '' || !isFormValid}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onCreateClick}
              >
                {'Create'}
              </button>
            )}

            {!createMode && (
              <button
                // disabled={!isFormValid || !canSave}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onSaveClick}
              >
                {'Save'}
              </button>
            )}

            {!createMode && (
              <button
                // disabled={!isFormValid || !canSave}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onDeleteClick}
              >
                {'Delete'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

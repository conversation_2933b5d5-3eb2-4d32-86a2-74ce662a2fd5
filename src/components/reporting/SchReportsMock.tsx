// import { ScheduledReport } from './SchReportingMockData';

// export const SchReportsMock: SuccessResponseType<ScheduledReport[]> = {
//   Data: {
//     content: [
//       {
//         id: 'SR-7f4e1d61-ff20-4b89-9759-d182e037acd2',
//         name: 'Weekly CO2 Repor2222t',
//         description: 'Generates and sends CO2 data logs every week',
//         status: 'ACTIVE',
//         eventId: 'ET-fa800c49-7cf7-45b9-ad2e-b04fac0eaec8',
//         eventName: 'redmond_office_airthings',
//         filterInfo: {
//           filters: {},
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: false,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 0 9 ? * MON,FRI',
//           readableCronExpression: 'at 09:00 at Monday and Friday days',
//           type: 'WEEKLY',
//           config: {
//             hour: 9,
//             minute: 0,
//             daysOfWeek: ['1', '5'],
//             dayOfMonth: null,
//           },
//           lastRunAt: null,
//           nextRunAt: '2025-06-20T06:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: ['<EMAIL>', '<EMAIL>'],
//           },
//           status: 'UNKNOWN',
//           lastAttemptAt: null,
//           failureDetails: null,
//         },
//         createdAt: '2025-06-16T15:28:40.406Z',
//         updatedAt: null,
//       },
//       {
//         id: 'SR-001',
//         name: 'Weekly CO2 Report',
//         description: 'Generates and sends CO2 data logs every week',
//         active: true,
//         eventId: 'ET-CO2-001',
//         eventName: 'redmond_office_airthings',
//         filterInfo: {
//           filters: {
//             redmond_office_airthings_co2: { gte: 600 },
//             identifierField: {},
//           },
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: false,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 0 9 ? * MON,FRI',
//           readableCronExpression: 'at 09:00 on Monday and Friday',
//           type: 'WEEKLY',
//           config: {
//             hour: 9,
//             minute: 0,
//             daysOfWeek: ['1', '5'],
//             dayOfMonth: null,
//           },
//           lastRunAt: null,
//           nextRunAt: '2025-06-13T06:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: ['<EMAIL>', '<EMAIL>'],
//           },
//           status: 'UNKNOWN',
//           lastAttemptAt: null,
//           failureDetails: null,
//         },
//         createdAt: '2025-06-12T10:00:00Z',
//         updatedAt: null,
//       },
//       {
//         id: 'SR-002',
//         name: 'Daily Temperature Report',
//         description: 'Reports average temperature levels',
//         active: true,
//         eventId: 'ET-TEMP-001',
//         eventName: 'temp_monitoring_event',
//         filterInfo: {
//           filters: {
//             current_temp: { gt: 30 },
//             sensor_id: {},
//           },
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: false,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 6 * * *',
//           readableCronExpression: 'at 06:00 every day',
//           type: 'DAILY',
//           config: {
//             hour: 6,
//             minute: 0,
//             daysOfWeek: [],
//             dayOfMonth: null,
//           },
//           lastRunAt: null,
//           nextRunAt: '2025-06-14T06:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: ['<EMAIL>'],
//           },
//           status: 'PENDING',
//           lastAttemptAt: null,
//           failureDetails: null,
//         },
//         createdAt: '2025-06-11T09:00:00Z',
//         updatedAt: null,
//       },
//       {
//         id: 'SR-003',
//         name: 'Battery Check Alert',
//         description: 'Detects devices with low battery',
//         status: 'ACTIVE',
//         eventId: 'ET-BATT-002',
//         eventName: 'battery_monitoring',
//         filterInfo: {
//           filters: {
//             battery_level: { lte: 20 },
//             charging: { eq: false },
//           },
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: true,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 */4 * * *',
//           readableCronExpression: 'every 4 hours',
//           type: 'DAILY',
//           config: {
//             hour: 0,
//             minute: 0,
//             daysOfWeek: [],
//             dayOfMonth: null,
//           },
//           lastRunAt: '2025-06-13T04:00:00Z',
//           nextRunAt: '2025-06-13T08:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: [],
//           },
//           status: 'SENT',
//           lastAttemptAt: '2025-06-13T04:05:00Z',
//           failureDetails: null,
//         },
//         createdAt: '2025-05-30T08:00:00Z',
//         updatedAt: '2025-06-12T14:00:00Z',
//       },
//       {
//         id: 'SR-004',
//         name: 'Weekly Activity Summary',
//         description: 'Summarizes weekly movement metrics',
//         active: true,
//         eventId: 'ET-ACT-101',
//         eventName: 'movement_tracking_event',
//         filterInfo: {
//           filters: {
//             total_steps: { gte: 5000 },
//             mobile_platform: { like: 'iOS' },
//           },
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: false,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 8 * * 1',
//           readableCronExpression: 'at 08:00 every Monday',
//           type: 'WEEKLY',
//           config: {
//             hour: 8,
//             minute: 0,
//             daysOfWeek: ['1'],
//             dayOfMonth: null,
//           },
//           lastRunAt: '2025-06-09T08:00:00Z',
//           nextRunAt: '2025-06-16T08:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: ['<EMAIL>'],
//           },
//           status: 'SENT',
//           lastAttemptAt: '2025-06-09T08:01:00Z',
//           failureDetails: null,
//         },
//         createdAt: '2025-06-01T12:00:00Z',
//         updatedAt: '2025-06-12T14:30:00Z',
//       },
//       {
//         id: 'SR-005',
//         name: 'Monthly Device Audit',
//         description: 'Generates audit of all devices every month',
//         active: true,
//         eventId: 'ET-AUD-009',
//         eventName: 'device_audit_event',
//         filterInfo: {
//           filters: {
//             device_type: { like: 'sensor' },
//             mobile_sdk_version: {},
//           },
//           operator: 'AND',
//           sort: {
//             field: '@timestamp',
//             asc: true,
//           },
//         },
//         scheduleInfo: {
//           cronExpression: '0 0 1 * *',
//           readableCronExpression: 'at 00:00 on the first of every month',
//           type: 'MONTHLY',
//           config: {
//             hour: 0,
//             minute: 0,
//             daysOfWeek: [],
//             dayOfMonth: 1,
//           },
//           lastRunAt: '2025-06-01T00:00:00Z',
//           nextRunAt: '2025-07-01T00:00:00Z',
//         },
//         deliveryInfo: {
//           target: {
//             targetType: 'EMAIL',
//             targetRecipients: ['<EMAIL>'],
//           },
//           status: 'PENDING',
//           lastAttemptAt: null,
//           failureDetails: null,
//         },
//         createdAt: '2025-05-01T00:00:00Z',
//         updatedAt: null,
//       },
//     ],
//   },
//   Error: null,
//   Code: 200,
//   Timestamp: '2025/06/13 14:20:24',
//   Message: 'Event log fields successfully listed.',
// };

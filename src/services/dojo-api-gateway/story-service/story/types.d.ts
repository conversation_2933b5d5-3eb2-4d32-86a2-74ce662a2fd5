type CreateStoryPayload = {
  name: string;
  description: string;
  user: string;
  folderId: string;
};

type RenameStoryPayload = {
  oldStoryName: string;
  newStoryName: string;
  description: string;
};

type RenameStatePayload = {
  stateAccessInfo: StateAccessInfo;
  newStateName: string;
};

type UpdateStateTypePayload = {
  stateAccessInfo: StateAccessInfo;
  newStateType: string;
};

type RemoveStatePayload = {
  stateAccessInfo: StateAccessInfo;
};

type AddPlayerVariablePayload = {
  storyName: string;
  parameterDto: {
    name: string;
    isExpression: boolean;
    pmValue: {
      valueType: 'EVENT' | string;
      javaType: JavaType;
      value: {
        eventParameterInfo: EventParameterInfo;
      };
    };
  };
};

type DeletePlayerVariablePayload = {
  storyName: string;
  dynamicParameterId: string;
};

type GetRequiredFieldsPayload = {
  storyName: string;
  eventName: string;
};

type UpsertRequiredFieldsPayload = {
  storyName: string;
  eventName: string;
  requiredEventFieldList: Array<{
    id?: string;
    fieldName: string;
    path: string;
    isNecessary: boolean;
    alreadyRequired: boolean;
  }>;
};

type RequiredFieldsSelectList = {
  storyName: string;
  eventName: string;
};

type EditEventLifeCyclePayload = {
  storyId: string;
  storyName: string;
  eventId: string;
  eventName: string;
  executionType: string;
  totalExecutionCount: string;
};

type GetLogsPayload = {
  storyName: string;
  checksTimeRange: boolean;
  timeRangeDto: {
    startTime: string;
    endTime: string;
    timeFieldName: string;
  };
  filtersByValue: boolean;
  filterValue: {
    productClass: string;
    uniqueIdentifier: string;
  };
};

type LogQueryPayload = {
  eventName: string;
  timeRange?: {
    from: string;
    to: string;
  };
  filters?: any;
  operator: string;
  sort: {
    field: string;
    asc: boolean;
  };
  pagination: {
    page: number;
    size: number;
  };
};
